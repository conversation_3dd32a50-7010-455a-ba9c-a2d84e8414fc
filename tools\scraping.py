from mcp.server.fastmcp import Context
import os
import logging

logging.basicConfig(filename="debug_handle_user_prompt.log", level=logging.INFO, format="%(asctime)s %(message)s")

# Get All html

import json
from typing import Any

def format_accessibility_tree(node: dict, depth: int = 0) -> str:
    """
    Recursively format the accessibility tree node into a readable indented structure.
    """
    indent = "  " * depth
    ref = f"[ref={node.get('name') or f'e{hash(str(node))%10000}'}]"  # fallback ref
    role = node.get("role", "generic")
    name = node.get("name")
    description = f"{role}"
    if name:
        if role in ["textbox", "combobox", "button"]:
            description += f" \"{name}\""
        else:
            description += f": {name}"
    description += f" {ref}"

    lines = [f"{indent}- {description.strip()}"]

    for child in node.get("children", []):
        lines.append(format_accessibility_tree(child, depth + 1))

    return "\n".join(lines)

async def get_html_content(ctx: Context, url: str) -> str:
    """
    Fetch the HTML content of a web page using Playwright.
    Args:
        ctx: MCP context (provides Playwright context)
        url: The URL of the page to fetch
    Returns:
        The HTML content as a string
    """
    try:
        browser = ctx.request_context.lifespan_context.browser
        page = await browser.new_page()
        await page.goto(url)
        content = await page.content()
        await page.close()
        return content
    except Exception as e:
        return f"Error fetching HTML: {str(e)}"
    

# Get html snapshot
async def get_html_snapshot(ctx: Context, url: str) -> str:
    """
    Generate a formatted semantic accessibility snapshot of a web page.

    Args:
        ctx: MCP context with browser.
        url: URL of the target page.

    Returns:
        A readable tree of roles, labels, and element refs.
    """
    try:
        browser = ctx.request_context.lifespan_context.browser
        page = await browser.new_page()
        await page.goto(url, timeout=30000)
        await page.wait_for_load_state("networkidle")

        snapshot_tree = await page.accessibility.snapshot()
        if not snapshot_tree:
            return "No accessible content found."

        formatted = format_accessibility_tree(snapshot_tree)
        await page.close()
        return formatted

    except Exception as e:
        return f"Error capturing snapshot of '{url}': {str(e)}"
# fill the fields and submit the form with a user comment
# Autofill and submit form
# async def autofill_form(ctx: Context, url: str, steps: list[dict]) -> str:
#     """
#     Autofill a form on the given URL using a sequence of user-defined steps.
    
#     Args:
#         ctx: MCP context containing the Playwright browser instance.
#         url: The URL of the page containing the form.
#         steps: A list of dictionaries representing form actions:
#                Each step should have 'action' ("fill" or "click"), 'selector', and optionally 'value'.

#     Example steps:
#         [
#             { "action": "fill", "selector": "#firstName", "value": "Saiju" },
#             { "action": "fill", "selector": "#lastName", "value": "Sunny" },
#             { "action": "fill", "selector": "#dob", "value": "05-Mar-2000" },
#             { "action": "fill", "selector": "#phone", "value": "9876543210" },
#             { "action": "fill", "selector": "#email", "value": "<EMAIL>" },
#             { "action": "select", "selector": "#gender", "value": "Others" },
#             { "action": "fill", "selector": "#otherGender", "value": "Non-binary" },
#             { "action": "click", "selector": "button[type='submit']" }
#         ]

#     Returns:
#         Final HTML after form interaction.
#     """
#     try:
#         browser = ctx.request_context.lifespan_context.browser
#         page = await browser.new_page()
#         await page.goto(url)
#         await page.wait_for_load_state("networkidle")

#         for step in steps:
#             action = step.get("action")
#             selector = step.get("selector")
#             value = step.get("value", "")

#             if action == "fill":
#                 await page.fill(selector, value)
#             elif action == "click":
#                 await page.click(selector)
#             else:
#                 return f"Unsupported action: {action}"

#         await page.wait_for_timeout(2000)  # Wait after submit (e.g., navigation)

#         content = await page.content()
#         await page.close()
#         return content

#     except Exception as e:
#         return f"Error autofilling form: {str(e)}"
    
######################
async def browser_snapshot(ctx: Context, params: dict) -> str:
    browser = ctx.request_context.lifespan_context.browser
    page = await browser.new_page()
    await page.goto("about:blank")  # Load a blank page
    snapshot = await page.accessibility.snapshot()
    await page.close()
    return snapshot

async def browser_click(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    selector = params["ref"]
    await page.click(selector)
    return f"Clicked on {params['element']}"


async def browser_hover(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    selector = params["ref"]
    await page.hover(selector)
    return f"Hovered over {params['element']}"


async def browser_drag(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    start = page.locator(params["startRef"])
    end = page.locator(params["endRef"])
    await start.drag_to(end)
    return f"Dragged from {params['startElement']} to {params['endElement']}"


async def browser_type(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    selector = params["ref"]
    text = params["text"]
    slowly = params.get("slowly", False)
    submit = params.get("submit", False)

    if slowly:
        for ch in text:
            await page.locator(selector).type(ch, delay=100)
    else:
        await page.fill(selector, text)

    if submit:
        await page.press(selector, "Enter")

    return f"Typed '{text}' into {params['element']}"


async def browser_upload(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    selector = params["ref"]
    file_path = params["file"]

    if not os.path.exists(file_path):
        return f"File not found: {file_path}"

    input_handle = await page.query_selector(selector)
    await input_handle.set_input_files(file_path)

    return f"Uploaded file to {params['element']}"


async def browser_select_option(ctx: Context, params: dict) -> str:
    """
    Selects one or more options in a dropdown <select> field.

    Args:
        ctx: MCP context with Playwright browser.
        params: Dictionary with:
            - element: (str) Human-readable label
            - ref: (str) Selector string
            - values: (list[str]) Option values to select

    Example:
        {
          "element": "Gender Dropdown",
          "ref": "#gender",
          "values": ["Others"]
        }

    Returns:
        Success message or error.
    """
    try:
        page = await ctx.ensure_page()
        selector = params["ref"]
        values = params["values"]

        print(f"// Select {values} in {selector}")
        await page.select_option(selector, values)

        return f"Selected {values} in {params['element']}"

    except Exception as e:
        return f"Error selecting option: {str(e)}"
    

async def autofill_form(ctx: Context, url: str, steps: list[dict]) -> str:
    """
    Autofill a form on the given URL using a sequence of user-defined steps.

    Args:
        ctx: MCP context containing the Playwright browser instance.
        url: The URL of the page containing the form.
        steps: A list of dictionaries representing form actions:
               Each step should have 'action', 'selector', and optionally 'value'.

    Supported actions:
        - fill: Fill a field
        - click: Click a button or element
        - upload: Upload a file to an <input type="file">

    Example:
        [
            { "action": "fill", "selector": "#firstName", "value": "Saiju" },
            { "action": "fill", "selector": "#lastName", "value": "Sunny" },
            { "action": "fill", "selector": "#dob", "value": "05-Mar-2000" },
            { "action": "fill", "selector": "#phone", "value": "9876543210" },
            { "action": "fill", "selector": "#email", "value": "<EMAIL>" },
            { "action": "select", "selector": "#gender", "value": "Others" },
            { "action": "fill", "selector": "#otherGender", "value": "Non-binary" },
            { "action": "click", "selector": "button[type='submit']" }
        ]

    Returns:
        Final HTML after form interaction or error message.
    """
    try:
        browser = ctx.request_context.lifespan_context.browser
        page = await browser.new_page()
        await page.goto(url)
        await page.wait_for_load_state("networkidle")

        for step in steps:
            action = step.get("action")
            selector = step.get("selector")
            value = step.get("value", "")
            
            if action == "fill":
                print(f"// Fill '{value}' into {selector}")
                await page.fill(selector, value)
                
            elif action == "type":
                print(f"// Type '{value}' into {selector}")
                await page.type(selector, value)
                
            elif action == "clear":
                print(f"// Clear content in {selector}")
                await page.fill(selector, "")
                
            elif action == "select":
                print(f"// Select {value} in {selector}")
                await page.select_option(selector, value)
                
            elif action == "click":
                print(f"// Click {selector}")
                await page.click(selector)
                
            elif action == "double_click":
                print(f"// Double click {selector}")
                await page.dblclick(selector)
                
            elif action == "right_click":
                print(f"// Right click {selector}")
                await page.click(selector, button="right")
                
            elif action == "check":
                print(f"// Check checkbox/radio {selector}")
                await page.check(selector)
                
            elif action == "uncheck":
                print(f"// Uncheck checkbox {selector}")
                await page.uncheck(selector)
                
            elif action == "toggle":
                print(f"// Toggle checkbox {selector}")
                is_checked = await page.is_checked(selector)
                if is_checked:
                    await page.uncheck(selector)
                else:
                    await page.check(selector)
                    
            elif action == "focus":
                print(f"// Focus on {selector}")
                await page.focus(selector)
                
            elif action == "blur":
                print(f"// Remove focus from {selector}")
                await page.evaluate(f"document.querySelector('{selector}').blur()")
                
            elif action == "hover":
                print(f"// Hover over {selector}")
                await page.hover(selector)
                
            elif action == "scroll_into_view":
                print(f"// Scroll {selector} into view")
                await page.locator(selector).scroll_into_view_if_needed()
                
            elif action == "upload":
                print(f"// Upload '{value}' into {selector}")
                if not os.path.exists(value):
                    return f"File not found: {value}"
                input_handle = await page.query_selector(selector)
                if not input_handle:
                    return f"Input element not found: {selector}"
                await input_handle.set_input_files(value)
                
            elif action == "upload_multiple":
                print(f"// Upload multiple files '{value}' into {selector}")
                files = value.split(",") if isinstance(value, str) else value
                for file_path in files:
                    if not os.path.exists(file_path.strip()):
                        return f"File not found: {file_path.strip()}"
                input_handle = await page.query_selector(selector)
                if not input_handle:
                    return f"Input element not found: {selector}"
                await input_handle.set_input_files([f.strip() for f in files])
                
            elif action == "select_text":
                print(f"// Select all text in {selector}")
                await page.focus(selector)
                await page.keyboard.press("Control+a")
                
            elif action == "copy":
                print(f"// Copy content from {selector}")
                await page.focus(selector)
                await page.keyboard.press("Control+a")
                await page.keyboard.press("Control+c")
                
            elif action == "paste":
                print(f"// Paste into {selector}")
                await page.focus(selector)
                await page.keyboard.press("Control+v")
                
            elif action == "press_key":
                print(f"// Press key '{value}' in {selector}")
                await page.focus(selector)
                await page.keyboard.press(value)
                
            elif action == "press_keys":
                print(f"// Press key combination '{value}' in {selector}")
                await page.focus(selector)
                keys = value.split("+")
                for key in keys[:-1]:
                    await page.keyboard.down(key)
                await page.keyboard.press(keys[-1])
                for key in reversed(keys[:-1]):
                    await page.keyboard.up(key)
                    
            elif action == "drag_and_drop":
                print(f"// Drag from {selector} to {value}")
                await page.drag_and_drop(selector, value)
                
            elif action == "wait_for_element":
                print(f"// Wait for element {selector} to be visible")
                await page.wait_for_selector(selector, state="visible", timeout=int(value) if value else 30000)
                
            elif action == "wait_for_text":
                print(f"// Wait for text '{value}' to appear")
                await page.wait_for_function(f"document.body.innerText.includes('{value}')")
                
            elif action == "scroll":
                print(f"// Scroll page by {value} pixels")
                x, y = (int(v) for v in value.split(",")) if "," in value else (0, int(value))
                await page.mouse.wheel(x, y)
                
            elif action == "screenshot":
                print(f"// Take screenshot and save as {value}")
                await page.screenshot(path=value)
                
            elif action == "get_text":
                print(f"// Get text from {selector}")
                text = await page.inner_text(selector)
                print(f"Text content: {text}")
                
            elif action == "get_value":
                print(f"// Get value from {selector}")
                value_content = await page.input_value(selector)
                print(f"Input value: {value_content}")
                
            elif action == "get_attribute":
                print(f"// Get attribute '{value}' from {selector}")
                attr_value = await page.get_attribute(selector, value)
                print(f"Attribute {value}: {attr_value}")
                
            elif action == "set_attribute":
                print(f"// Set attribute on {selector}")
                attr_name, attr_value = value.split("=", 1) if "=" in value else (value, "")
                await page.evaluate(f"document.querySelector('{selector}').setAttribute('{attr_name}', '{attr_value}')")
                
            elif action == "remove_attribute":
                print(f"// Remove attribute '{value}' from {selector}")
                await page.evaluate(f"document.querySelector('{selector}').removeAttribute('{value}')")
                
            elif action == "execute_js":
                print(f"// Execute JavaScript: {value}")
                result = await page.evaluate(value)
                if result:
                    print(f"JS Result: {result}")
                    
            elif action == "wait":
                print(f"// Wait for {value} milliseconds")
                await page.wait_for_timeout(int(value))
                
            elif action == "reload":
                print(f"// Reload page")
                await page.reload()
                
            elif action == "go_back":
                print(f"// Navigate back")
                await page.go_back()
                
            elif action == "go_forward":
                print(f"// Navigate forward")
                await page.go_forward()
                
            elif action == "navigate":
                print(f"// Navigate to {value}")
                await page.goto(value)
                
            elif action == "switch_frame":
                print(f"// Switch to frame {selector}")
                frame = page.frame(selector)
                if frame:
                    # Continue operations in this frame context
                    pass
                else:
                    return f"Frame not found: {selector}"
                    
            elif action == "switch_tab":
                print(f"// Switch to tab {value}")
                pages = page.context.pages
                if int(value) < len(pages):
                    await pages[int(value)].bring_to_front()
                else:
                    return f"Tab index {value} not found"
                    
            elif action == "close_tab":
                print(f"// Close current tab")
                await page.close()
                
            elif action == "new_tab":
                print(f"// Open new tab with URL {value}")
                new_page = await page.context.new_page()
                if value:
                    await new_page.goto(value)
                    
            elif action == "accept_dialog":
                print(f"// Accept dialog/alert")
                page.on("dialog", lambda dialog: dialog.accept())
                
            elif action == "dismiss_dialog":
                print(f"// Dismiss dialog/alert")
                page.on("dialog", lambda dialog: dialog.dismiss())
                
            elif action == "handle_dialog":
                print(f"// Handle dialog with text '{value}'")
                page.on("dialog", lambda dialog: dialog.accept(value) if value else dialog.accept())
                
            else:
                return f"Unsupported action: {action}"
        await page.wait_for_timeout(2000)  # wait to allow form submission/navigation
        content = await page.content()
        await page.close()
        return content

    except Exception as e:
        return f"Error autofilling form: {str(e)}"
    
async def handle_user_prompt(ctx: Context, prompt: str) -> str:
    """
    Handle a user prompt to autofill a form using Groq.

    Args:
        ctx: MCP context with Playwright.
        prompt: Natural language instruction from user.

    Returns:
        HTML content after form submission or error message.
    """
    import httpx, re, json
    

    # try:
        # 1. Extract URL from prompt
    url_match = re.search(r"(https?://\S+)", prompt)
    logging.info(f"url_match: {url_match}")
    if not url_match:
        return "No valid URL found in the prompt."
    url = url_match.group(1)

    # 2. Get HTML content
    html = await get_html_snapshot(ctx, url)
    logging.info(f"html: {html[:500]}")  # log only first 500 chars
    if "Error" in html:
        return html

    # 3. Prepare Groq API request
    groq_payload = {
        "model": "qwen-qwq-32b",
        "messages": [
            {
                "role": "system",
                "content": (
                    "You are a helpful assistant that converts user instructions "
                    "and HTML into a JSON list of structured steps to fill and submit the form. "
                    "Return only JSON in this format:\n"
                    "[{ \"action\": \"fill\", \"selector\": \"#firstName\", \"value\": \"John\" }, ...]"
                )
            },
            {
                "role": "user",
                "content": f"Prompt:\n{prompt}\n\nHTML:\n{html}"
            }
        ]
    }

    headers = {
        "Authorization": "Bearer ********************************************************",
        "Content-Type": "application/json"
    }

    # 4. Call Groq API
    async with httpx.AsyncClient(timeout=60) as client:
        response = await client.post("https://api.groq.com/openai/v1/chat/completions", headers=headers, json=groq_payload)

    if response.status_code != 200:
        return f"Groq error: {response.text}"

    # 5. Extract steps from response
    content = response.json()["choices"][0]["message"]["content"]
    try:
        steps = json.loads(content)
    except json.JSONDecodeError:
        return f"Failed to parse steps JSON: {content}"

    if not isinstance(steps, list):
        return "Groq response is not a list of steps."
    logging.info(f"steps: {steps}")
    # 6. Run autofill
    result = await autofill_form(ctx, url, steps)
    logging.info(f"result: {result[:500]}")  # log only first 500 chars
    return result

    # except Exception as e:
    #     logging.exception("Error in handle_user_prompt")
    #     return f"Error handling prompt: {str(e)}"
