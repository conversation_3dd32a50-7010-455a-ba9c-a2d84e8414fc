# import asyncio

# from dotenv import load_dotenv
# from langchain_groq import Chat<PERSON><PERSON>q

# from mcp_use import <PERSON><PERSON><PERSON>, MCPClient
# import os

# async def run_memory_chat():
#     """Run a chat using MCPAgent's built-in conversation memory."""
#     # Load environment variables for API keys
#     load_dotenv()
#     os.environ["GROQ_API_KEY"]=os.getenv("GROQ_API_KEY")

#     # Config file path - change this to your config file
#     config_file = "config.json"

#     print("Initializing chat...")

#     # Create MCP client and agent with memory enabled
#     client = MCPClient.from_config_file(config_file)
#     llm = ChatGroq(model="qwen-qwq-32b")

#     # Create agent with memory_enabled=True
#     agent = MCPAgent(
#         llm=llm,
#         client=client,
#         max_steps=15,
#         memory_enabled=True,  # Enable built-in conversation memory
#     )

#     print("\n===== Interactive MCP Chat =====")
#     print("Type 'exit' or 'quit' to end the conversation")
#     print("Type 'clear' to clear conversation history")
#     print("==================================\n")

#     try:
#         # Main chat loop
#         while True:
#             # Get user input
#             user_input = input("\nYou: ")

#             # Check for exit command
#             if user_input.lower() in ["exit", "quit"]:
#                 print("Ending conversation...")
#                 break

#             # Check for clear history command
#             if user_input.lower() == "clear":
#                 agent.clear_conversation_history()
#                 print("Conversation history cleared.")
#                 continue

#             # Get response from agent
#             print("\nAssistant: ", end="", flush=True)

#             try:
#                 # Run the agent with the user input (memory handling is automatic)
#                 response = await agent.run(user_input)
#                 print(response)

#             except Exception as e:
#                 print(f"\nError: {e}")

#     finally:
#         # Clean up
#         if client and client.sessions:
#             await client.close_all_sessions()


# if __name__ == "__main__":
#     asyncio.run(run_memory_chat())



# import asyncio
# import os
# from dotenv import load_dotenv
# from langchain_openai import ChatOpenAI  # Use OpenAI-compatible interface

# from mcp_use import MCPAgent, MCPClient  # Assuming MCPAgent supports any LangChain-compatible LLM

# async def run_memory_chat():
#     """Run a chat using MCPAgent's built-in conversation memory."""
#     # Load environment variables for API keys
#     load_dotenv()
#     os.environ["OPENROUTER_API_KEY"] = os.getenv("OPENROUTER_API_KEY")

#     config_file = "config.json"

#     print("Initializing chat...")

#     # Create MCP client and agent with memory enabled
#     client = MCPClient.from_config_file(config_file)

#     # Use OpenRouter as an OpenAI-compatible endpoint
#     llm = ChatOpenAI(
#         openai_api_base="https://openrouter.ai/api/v1",
#         openai_api_key=os.environ["OPENROUTER_API_KEY"],
#         model="deepseek/deepseek-chat-v3-0324:free",  # Use a popular, free, and available model
#         temperature=0.3,
#         timeout=60  # Optional: set a timeout in seconds
#     )

#     # Create agent with memory_enabled=True
#     agent = MCPAgent(
#         llm=llm,
#         client=client,
#         max_steps=15,
#         memory_enabled=False,  # Enable built-in conversation memory
#     )

#     print("\n===== Interactive MCP Chat =====")
#     print("Type 'exit' or 'quit' to end the conversation")
#     print("Type 'clear' to clear conversation history")
#     print("==================================\n")

#     try:
#         # Main chat loop
#         while True:
#             user_input = input("\nYou: ")

#             if user_input.lower() in ["exit", "quit"]:
#                 print("Ending conversation...")
#                 break

#             if user_input.lower() == "clear":
#                 agent.clear_conversation_history()
#                 print("Conversation history cleared.")
#                 continue

#             print("\nAssistant: ", end="", flush=True)

#             try:
#                 response = await agent.run(user_input)
#                 print(response)
#             except Exception as e:
#                 print(f"\nError: {e}")

#     finally:
#         if client and client.sessions:
#             await client.close_all_sessions()


# if __name__ == "__main__":
#     asyncio.run(run_memory_chat())


import asyncio
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI  # Use OpenAI-compatible interface

from mcp_use import MCPAgent, MCPClient  # Assuming MCPAgent supports any LangChain-compatible LLM

def get_openai_api_key():
    # Try to get from environment or .env
    load_dotenv()
    return os.getenv("OPENAI_API_KEY")

async def run_memory_chat():
    """Run a chat using MCPAgent's built-in conversation memory with OpenAI."""
    # Load environment variables for API keys
    api_key = get_openai_api_key()
    if not api_key:
        raise ValueError("OPENAI_API_KEY not found in environment or .env file.")
    os.environ["OPENAI_API_KEY"] = api_key

    config_file = "config.json"

    print("Initializing chat...")

    # Create MCP client and agent with memory enabled
    client = MCPClient.from_config_file(config_file)

    # Use OpenAI endpoint
    llm = ChatOpenAI(
        openai_api_key=api_key,
        model="gpt-4.1",  # Use a popular OpenAI model
        temperature=0.3,
        timeout=60  # Optional: set a timeout in seconds
    )

    # Create agent with memory_enabled=True
    agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=30,
        memory_enabled=False,  # Enable built-in conversation memory
    )

    print("\n===== Interactive MCP Chat (OpenAI) =====")
    print("Type 'exit' or 'quit' to end the conversation")
    print("Type 'clear' to clear conversation history")
    print("==================================\n")

    try:
        # Main chat loop
        while True:
            user_input = input("\nYou: ")

            if user_input.lower() in ["exit", "quit"]:
                print("Ending conversation...")
                break

            if user_input.lower() == "clear":
                agent.clear_conversation_history()
                print("Conversation history cleared.")
                continue

            print("\nAssistant: ", end="", flush=True)

            try:
                response = await agent.run(user_input)
                print(response)
            except Exception as e:
                print(f"\nError: {e}")

    finally:
        if client and client.sessions:
            await client.close_all_sessions()


if __name__ == "__main__":
    asyncio.run(run_memory_chat()) 