from mcp.server.fastmcp import Context
import os
import logging
logging.basicConfig(filename="debug_handle_user_prompt.log", level=logging.INFO, format="%(asctime)s %(message)s")
from dotenv import load_dotenv
import subprocess
# Get All html
import shutil
import json
from typing import Any
from contextlib import asynccontextmanager
from dataclasses import dataclass
import asyncio
import os
from playwright.async_api import async_playwright
import uuid
import datetime
import re

# Store session pages: session_id -> page
session_pages = {}
# Store the last used session_id for automatic reuse
last_session_id = None
last_logged_session_id = None


async def ensure_browser(ctx):
    browser = ctx.request_context.lifespan_context.browser
    if browser is None or (hasattr(browser, 'is_closed') and browser.is_closed()):
        playwright = ctx.request_context.lifespan_context.playwright
        browser = await playwright.chromium.launch(headless=False)
        ctx.request_context.lifespan_context.browser = browser
    return browser


def format_accessibility_tree(node: dict, depth: int = 0) -> str:
    """
    Recursively format the accessibility tree node into a readable indented structure.
    """
    indent = "  " * depth
    ref = f"[ref={node.get('name') or f'e{hash(str(node))%10000}'}]"  # fallback ref
    role = node.get("role", "generic")
    name = node.get("name")
    description = f"{role}"
    if name:
        if role in ["textbox", "combobox", "button"]:
            description += f" \"{name}\""
        else:
            description += f": {name}"
    description += f" {ref}"

    lines = [f"{indent}- {description.strip()}"]

    for child in node.get("children", []):
        lines.append(format_accessibility_tree(child, depth + 1))

    return "\n".join(lines)

async def get_html_content(ctx: Context, url: str,session_id, url=None) -> str:
    """
    Fetch the HTML content of a web page using Playwright.
    Args:
        ctx: MCP context (provides Playwright context)
        url: The URL of the page to fetch
    Returns:
        The HTML content as a string
    """
    try:
        browser = ctx.request_context.lifespan_context.browser
        page = await browser.new_page()
        await page.goto(url)
        content = await page.content()
        await page.close()
        return content
    except Exception as e:
        return f"Error fetching HTML: {str(e)}"
    

# Get html snapshot
async def get_accessibility_snapshot(ctx: Context, url: str, session_id: str = None) -> str:
    """
    Capture a structured accessibility snapshot of the current page (read-only).
    Args:
        ctx: MCP context (provides Playwright context)
        url: The URL of the page to fetch
        session_id: Optional session id for page reuse
    Returns:
        The accessibility snapshot as a JSON string
    """
    try:
        log_playwright_action("get_accessibility_snapshot", {"url": url, "session_id": session_id})
        page, session_id = await get_or_create_session_page(ctx, session_id, url)
        snapshot = await page.accessibility.snapshot()
        return json.dumps({"session_id": session_id, "snapshot": snapshot}, indent=2)
    except Exception as e:
        return f"Error capturing accessibility snapshot: {str(e)}"



async def get_or_create_session_page(ctx, session_id, url=None):
    global last_session_id
    browser = await ensure_browser(ctx)
    if not session_id:
        session_id = last_session_id
    if session_id and session_id in session_pages:
        last_session_id = session_id
        return session_pages[session_id], session_id
    else:
        page = await browser.new_page()
        if url:
            await page.goto(url)
        # Generate a new session_id if not provided
        if not session_id:
            session_id = str(uuid.uuid4())
        session_pages[session_id] = page
        last_session_id = session_id
        return page, session_id
    
def log_playwright_action(action: str, params: dict):
    global last_logged_session_id
    session_id = params.get("session_id")
    clear_log = False
    if session_id and session_id != last_logged_session_id:
        clear_log = True
        last_logged_session_id = session_id
    try:
        if clear_log:
            # Clear the log file if a new session_id is detected
            with open("../playwright_logs/playwright_actions.log", "w", encoding="utf-8") as f:
                pass  # Truncate the file
        with open("../playwright_logs/playwright_actions.log", "a", encoding="utf-8") as f:
            timestamp = datetime.datetime.now().isoformat()
            f.write(f"{timestamp} | {action} | {json.dumps(params)}\n")
    except Exception as e:
        pass  # Don't let logging break the main flow

async def navigate_to_url(ctx: Context, url: str) -> str:
    """
    Open a new page, navigate to the given URL, create a new session_id, and return it.
    All following tools will use this session_id until the browser is closed.
    """
    
    try:
        browser = await ensure_browser(ctx)
        # Always create a new session and page
        session_id = str(uuid.uuid4())
        page = await browser.new_page()
        await page.goto(url)
        session_pages[session_id] = page
        global last_session_id
        last_session_id = session_id
        log_playwright_action("navigate_to_url", {"url": url, "session_id": session_id})
        return f"Navigated to {url} with session_id: {session_id}"
    except Exception as e:
        return f"Error navigating to URL: {str(e)}"
    

async def close_all_sessions_and_browser(ctx: Context, session_id: str = None) -> str:
    """
    This tool should always be called last, after all other actions, to ensure all browser sessions and resources are properly cleaned up.
    Close a specific session page if session_id is provided, otherwise close all open session pages and the browser instance.
    """
    log_playwright_action("close_all_sessions_and_browser", {"session_id": session_id})
    try:
        browser = ctx.request_context.lifespan_context.browser
        global last_session_id
        if session_id:
            # Close only the specified session/page
            page = session_pages.get(session_id)
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
                del session_pages[session_id]
                if last_session_id == session_id:
                    last_session_id = None
                return f"Session {session_id} closed."
            else:
                return f"Session {session_id} not found."
        else:
            # Close all open pages and the browser
            for page in list(session_pages.values()):
                try:
                    await page.close()
                except Exception:
                    pass
            session_pages.clear()
            last_session_id = None
            await browser.close()
            ctx.request_context.lifespan_context.browser = None
            return "All sessions and browser closed."
    except Exception as e:
        return f"Error closing sessions and browser: {str(e)}"




async def click_accessible_element(ctx: Context, url: str, accessible_name: str, role: str, session_id: str = None) -> str:
    """
    Click an element on the page using its accessible name (from the accessibility tree) and role.
    Args:
        ctx: MCP context (provides Playwright context)
        url: The URL of the page to fetch
        accessible_name: The accessible name of the element to click
        role: The role of the element to click (e.g., 'button')
        session_id: Optional session id for page reuse
    Returns:
        A message indicating success or failure
    """
    log_playwright_action("click_accessible_element", {"url": url, "accessible_name": accessible_name, "role": role, "session_id": session_id})
    try:
        browser = ctx.request_context.lifespan_context.browser
        page, session_id = await get_or_create_session_page(ctx, session_id, url)
        locator = page.get_by_role(role, name=accessible_name)
        count = await locator.count()
        if count == 0:
            return f"No element found with accessible name: {accessible_name} and role: {role}"
        await locator.first.click()
        return f"Clicked element with accessible name: {accessible_name} and role: {role} (session_id: {session_id})"
    except Exception as e:
        return f"Error clicking accessible element: {str(e)}"


async def type_into_accessible_input(
    ctx: Context,
    url: str,
    accessible_name: str,
    text: str,
    role: str,
    submit: bool = False,
    slowly: bool = False,
    session_id: str = None
) -> str:
    """
    Type text into an input field identified by its accessible name (from the accessibility tree) and role.
    Args:
        ctx: MCP context (provides Playwright context)
        url: The URL of the page to fetch
        accessible_name: The accessible name of the input field
        text: The text to type into the input field
        role: The role of the input field (e.g., 'textbox')
        submit: Whether to press Enter after typing (default: False)
        slowly: Whether to type one character at a time (default: False)
        session_id: Optional session id for page reuse
    Returns:
        A message indicating success or failure
    """
    log_playwright_action("type_into_accessible_input", {
        "url": url,
        "accessible_name": accessible_name,
        "text": text,
        "role": role,
        "submit": submit,
        "slowly": slowly,
        "session_id": session_id
    })
    try:
        browser = ctx.request_context.lifespan_context.browser
        page, session_id = await get_or_create_session_page(ctx, session_id, url)
        locator = page.get_by_role(role, name=accessible_name)
        count = await locator.count()
        if count == 0:
            return f"No input field found with accessible name: {accessible_name} and role: {role}"
        input_box = locator.first
        if slowly:
            for char in text:
                await input_box.type(char)
            if submit:
                await input_box.press("Enter")
        else:
            await input_box.fill("")
            await input_box.type(text)
            if submit:
                await input_box.press("Enter")
        return f"Typed text into input field with accessible name: {accessible_name} and role: {role} (session_id: {session_id})"
    except Exception as e:
        return f"Error typing into accessible input: {str(e)}"


async def select_dropdown_value(
    ctx: Context,
    url: str,
    accessible_name: str,
    value: str,
    role: str,
    session_id: str = None
 ) -> str:
    """
    Select a value from a dropdown (combobox) on the page by accessible name and role.
    Args:
        ctx: MCP context (provides Playwright context)
        url: The URL of the page to fetch
        accessible_name: The accessible name of the dropdown
        value: The value to select
        role: The role of the dropdown (e.g., 'combobox')
        session_id: Optional session id for page reuse
    Returns:
        A message indicating success or failure
    """
    log_playwright_action("select_dropdown_value", {
        "url": url,
        "accessible_name": accessible_name,
        "value": value,
        "role": role,
        "session_id": session_id
    })
    try:
        browser = ctx.request_context.lifespan_context.browser
        page, session_id = await get_or_create_session_page(ctx, session_id, url)
        locator = page.get_by_role(role, name=accessible_name)
        count = await locator.count()
        if count == 0:
            return f"No dropdown found with accessible name: {accessible_name} and role: {role}"
        dropdown = locator.first
        await dropdown.select_option(value)
        return f"Selected value '{value}' in dropdown with accessible name: {accessible_name} and role: {role} (session_id: {session_id})"
    except Exception as e:
        return f"Error selecting dropdown value: {str(e)}"


async def log_field_errors(ctx: Context, errors: dict, session_id: str = None) -> str:
    """
    Log error messages for multiple fields in the current session. This is used to save all error messages fetched from the form in one call.
    Args:
        ctx: MCP context (provides Playwright context)
        errors: A dictionary mapping field names to error messages
        session_id: The session id for the current session
    Returns:
        A confirmation message
    """
    log_playwright_action("field_errors", {
        "errors": errors,
        "session_id": session_id
    })
    return f"Logged errors for session {session_id}."


async def save_playwright_code(ctx: Context) -> str:
    """
    Generate a Playwright test script from the logged actions and save it as playwright_code/generated_script.spec.ts (UTF-8).
    If field errors are logged, also generate assertions for error messages.
    Returns a message indicating success and the output file path.
    """
    try:
        output_lines = []
        error_assertions = []
        output_lines.append("import { test, expect } from '@playwright/test';\n")
        output_lines.append("\ntest('fill and submit form', async ({ page }) => {\n")
 
        with open("../playwright_logs/playwright_actions.log", encoding="utf-8") as f:
            for line in f:
                try:
                    _, action, params = line.strip().split(" | ", 2)
                    params = json.loads(params)
                    if action == "navigate_to_url":
                        output_lines.append(f"  await page.goto('{params['url']}');\n")
                    elif action == "wait_for_seconds":
                        seconds = params.get("seconds", 1)
                        output_lines.append(f"  await page.waitForTimeout({int(seconds) * 1000});\n")

                    elif action == "type_into_accessible_input":
                        role = params.get("role", "textbox")
                        output_lines.append(f"  await page.getByRole('{role}', {{ name: '{params['accessible_name']}' }}).fill('{params['text']}');\n")
                        if params.get("slowly"):
                            output_lines.append("  // Note: 'slowly' typing not natively supported in fill().\n")
                        if params.get("submit"):
                            output_lines.append(f"  await page.getByRole('{role}', {{ name: '{params['accessible_name']}' }}).press('Enter');\n")
                    elif action == "click_accessible_element":
                        role = params.get("role", "button")
                        output_lines.append(f"  await page.getByRole('{role}', {{ name: '{params['accessible_name']}' }}).click();\n")
                    elif action == "select_dropdown_value":
                        role = params.get("role", "combobox")
                        output_lines.append(f"  await page.getByRole('{role}', {{ name: '{params['accessible_name']}' }}).selectOption('{params['value']}');\n")
                    elif action == "field_errors":
                        errors = params.get("errors", {})
                        for field, error_msg in errors.items():
                            # Add assertion for error message
                            error_assertions.append(f'await expect(page.getByText("{error_msg}").toBeVisible();\n')
                    # Add more cases as needed for other tool actions
                except Exception:
                    continue
 
        # Insert error assertions after all actions
        output_lines.extend(error_assertions)
        output_lines.append("});\n")
 
        with open("../playwright_code/generated_script.spec.ts", "w", encoding="utf-8") as out:
            out.writelines(output_lines)
        return "Playwright script saved to ../playwright_code/enerated_script.spec.ts"
    except Exception as e:
        return f"Error saving Playwright script: {str(e)}"
    

async def scroll_page(
    ctx: Context,
    url: str = None,
    direction: str = "down",
    amount: int = 1000,
    delay_ms: int = 0,
    session_id: str = None
) -> dict:
    """
    Scrolls the page up or down by a specified amount, with optional delay.
    
    Args:
        ctx: MCP context
        url: Optional URL (used only if a session does not already exist)
        direction: "down" or "up"
        amount: Number of pixels to scroll
        delay_ms: Optional delay in milliseconds after scrolling
        session_id: Optional session ID to reuse page context

    Returns:
        Dict with message, session_id, and error if any
    """
    log_playwright_action("scroll_page", {
        "url": url,
        "direction": direction,
        "amount": amount,
        "delay_ms": delay_ms,
        "session_id": session_id
    })
    try:
        page = None
        if session_id and session_id in session_pages:
            page = session_pages[session_id]
        else:
            page, session_id = await get_or_create_session_page(ctx, session_id, url)

        scroll_value = amount if direction == "down" else -amount
        await page.evaluate(f"window.scrollBy(0, {scroll_value});")

        if delay_ms > 0:
            await asyncio.sleep(delay_ms / 1000)

        return {"message": f"Scrolled {direction} by {amount}px", "session_id": session_id}
    except Exception as e:
        return {"error": f"Scroll error: {str(e)}", "session_id": session_id}
    

#tool for wait

async def wait_for_seconds(
    ctx: Context,
    prompt: str,
    session_id: str = None
) -> dict:
    """
    Waits for a duration inferred from a natural language prompt, supports MCP sessions.

    Args:
        ctx: MCP context
        prompt: Instruction like "wait for 5 seconds"
        session_id: Optional session ID to preserve session continuity

    Returns:
        Dict with wait message and session ID
    """
    log_playwright_action("wait_for_seconds", {
        "prompt": prompt,
        "session_id": session_id
    })

    try:
        # Extract numeric duration
        match = re.search(r"(\d+(\.\d+)?)", prompt)
        if match:
            seconds = float(match.group(1))
        elif "half" in prompt.lower():
            seconds = 0.5
        elif "minute" in prompt.lower():
            seconds = 60
        else:
            seconds = 1.0  # fallback

        await asyncio.sleep(seconds)

        return {
            "message": f"Waited for {seconds} seconds",
            "session_id": session_id
        }
    except Exception as e:
        return {
            "error": f"Error during wait: {str(e)}",
            "session_id": session_id
        }
