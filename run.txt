uv add "mcp[cli]"
uv run mcp dev server/playwright_server.py for test
uv run mcp install server/playwright_server.py for add cloude


[
    { "action": "fill", "selector": "#firstName", "value": "Saiju" },
    { "action": "fill", "selector": "#lastName", "value": "Sunny" },
    { "action": "fill", "selector": "#dob", "value": "05-Mar-2000" },
    { "action": "fill", "selector": "#phone", "value": "9876543210" },
    { "action": "fill", "selector": "#email", "value": "<EMAIL>" },
    { "action": "fill", "selector": "#website", "value": "https://form2-4y5z.onrender.com/" },
    { "action": "select", "selector": "#gender", "value": "Others" },
    { "action": "fill", "selector": "#otherGender", "value": "Non-binary" },
    { "action": "click", "selector": "button[type='submit']" }
]