{"testDir": "./tests", "timeout": 30000, "retries": 0, "reporter": [["html", {"outputFolder": "./tests/playwright-reports", "open": "never"}]], "use": {"headless": false, "viewport": {"width": 1280, "height": 720}, "actionTimeout": 5000}, "projects": [{"name": "Chromium", "use": {"browserName": "chromium"}}, {"name": "Firefox", "use": {"browserName": "firefox"}}, {"name": "WebKit", "use": {"browserName": "webkit"}}, {"name": "Mobile Chrome", "use": {"browserName": "chromium", "userAgent": "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Mobile Safari/537.36", "viewport": {"width": 393, "height": 727}, "screen": {"width": 393, "height": 851}, "deviceScaleFactor": 2.75, "isMobile": true, "hasTouch": true, "defaultBrowserType": "chromium"}}]}