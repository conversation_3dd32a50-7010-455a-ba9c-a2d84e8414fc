from typing import Any
from mcp.server.fastmcp import FastMCP, Context
from contextlib import asynccontextmanager
from dataclasses import dataclass
import asyncio
import os
from playwright.async_api import async_playwright
import json
import uuid
import datetime
from tools import tools


# # Load the config JSON generated from dump-config.ts
# with open("playwright.config.json") as f:
#     config = json.load(f)

# # # Pull values from the shared config
# headless = config.get("use", {}).get("headless", True)
# viewport = config.get("use", {}).get("viewport", None)
# action_timeout = config.get("use", {}).get("actionTimeout", 0)
# Context dataclass for Playwright
@dataclass
class PlaywrightContext:
    playwright: Any
    browser: Any

@asynccontextmanager
async def playwright_lifespan(server: FastMCP):
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        try:
            yield PlaywrightContext(playwright=p, browser=browser)
        finally:
            await browser.close()

# Initialize FastMCP server
mcp = FastMCP(
    "playwright-html",
    description="MCP server for fetching HTML content using Playwright",
    lifespan=playwright_lifespan,
    host=os.getenv("HOST", "0.0.0.0"),
    port=os.getenv("PORT", "8050")
)
# for get html
mcp.tool(
    name="get-html",
    description="Fetch the full HTML content of a given web page using Playwright. "
                "Provide a URL and receive the raw HTML as a string."
)(tools.get_html_content)

mcp.tool(
    name="get-html-snapshot",
    description="Generate a semantic accessibility snapshot of a web page. "
                "Returns a readable tree of roles, labels, and element references for accessibility analysis. "
                "Input: URL. Output: formatted accessibility tree."
)(tools.get_accessibility_snapshot)

mcp.tool(
    name="navigate-to-url",
    description="Open a new browser page and navigate to the specified URL. "
                "Creates a new session_id for further actions. "
                "Input: URL. Output: Confirmation message with session_id."
)(tools.navigate_to_url)

mcp.tool(
    name="close-all-sessions-and-browser",
    description="Close all open browser sessions and the browser instance. "
                "Optionally, provide a session_id to close only a specific session. "
                "Input: Optional session_id. Output: Status message."
)(tools.close_all_sessions_and_browser)

mcp.tool(
    name="click-accessible-element",
    description="Click an element on the page by its accessible name and role (from the accessibility tree). "
                "Inputs: URL, accessible_name, role, optional session_id. "
                "Output: Success/failure message."
)(tools.click_accessible_element)

mcp.tool(
    name="type-into-accessible-input",
    description="Type text into an input field identified by accessible name and role. "
                "Supports optional submit (press Enter) and slow typing. "
                "Inputs: URL, accessible_name, text, role, optional submit, slowly, session_id. "
                "Output: Success/failure message."
)(tools.type_into_accessible_input)

mcp.tool(
    name="select-dropdown-value",
    description="Select a value from a dropdown (combobox) by accessible name and role. "
                "Inputs: URL, accessible_name, value, role, optional session_id. "
                "Output: Success/failure message."
)(tools.select_dropdown_value)

mcp.tool(
    name="scroll-page",
    description="Scroll the page up or down by a specified amount, with optional delay. "
                "Inputs: URL, direction (down/up), amount (pixels), delay_ms (milliseconds), optional session_id. "
                "Output: Dict with message, session_id, and error if any."
)(tools.scroll_page)

mcp.tool(
    name="wait-for-seconds",
    description="Wait for a specified duration, with optional session_id. "
                "Inputs: URL, duration (seconds), optional session_id. "
                "Output: Dict with message, session_id, and error if any."
)(tools.wait_for_seconds)
mcp.tool(
    name="save_playwright_code",
    description="""Generate a Playwright test script from the logged actions and save it as server/generated_script.spec.ts (UTF-8). "
    "If field errors are logged, also generate assertions for error messages. "
    "Inputs: None. "
    "Output: Success/failure message."""
    )(tools.save_playwright_code)


# if __name__ == "__main__":
#     asyncio.run(mcp.run_stdio_async())