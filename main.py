from typing import Any
from mcp.server.fastmcp import FastMC<PERSON>, Context
from contextlib import asynccontextmanager
from dataclasses import dataclass
import asyncio
import os
from playwright.async_api import async_playwright
from tools import scraping

# Context dataclass for Playwright
@dataclass
class PlaywrightContext:
    playwright: Any
    browser: Any

@asynccontextmanager
async def playwright_lifespan(server: FastMCP):
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        try:
            yield PlaywrightContext(playwright=p, browser=browser)
        finally:
            await browser.close()

# Initialize FastMCP server
mcp = FastMCP(
    "playwright-html",
    description="MCP server for fetching HTML content using Playwright",
    lifespan=playwright_lifespan,
    host=os.getenv("HOST", "0.0.0.0"),
    port=os.getenv("PORT", "8050")
)

# for get html
mcp.tool(name="Get HTML", description="Fetch the HTML of a given web page.")(scraping.get_html_content)
mcp.tool(name="Get Snapshot", description="Fetch the HTML snapshot of a given web page.")(scraping.get_html_snapshot)
mcp.tool(name="Autofill Form", description="Autofill and submit a form using user-defined steps")(scraping.autofill_form)
mcp.tool(name="handle_user_prompt", description="Handle user prompt")(scraping.handle_user_prompt)
