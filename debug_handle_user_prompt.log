2025-07-02 20:11:37,822 Processing request of type CallToolRequest
2025-07-02 20:11:50,384 Processing request of type CallToolRequest
2025-07-02 20:11:50,384 url_match: <re.Match object; span=(14, 46), match='https://form2-4y5z.onrender.com/'>
2025-07-02 20:11:52,110 html: <!DOCTYPE html><html lang="en"><head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>UserForm</title>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Flatpickr CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

  <style>
    .error-message {
      color: red;
      font-size: 0.875em;
    }
  </st
2025-07-02 20:13:55,854 Processing request of type CallToolRequest
2025-07-02 20:13:55,855 url_match: <re.Match object; span=(14, 46), match='https://form2-4y5z.onrender.com/'>
2025-07-02 20:13:57,028 html: <!DOCTYPE html><html lang="en"><head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>UserForm</title>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Flatpickr CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

  <style>
    .error-message {
      color: red;
      font-size: 0.875em;
    }
  </st
2025-07-02 20:15:32,460 Processing request of type CallToolRequest
2025-07-02 20:15:32,462 url_match: <re.Match object; span=(14, 46), match='https://form2-4y5z.onrender.com/'>
2025-07-02 20:15:33,921 html: - WebArea: UserForm [ref=UserForm]
  - heading: User Registration [ref=User Registration]
  - text: First Name [ref=First Name]
  - textbox "First Name" [ref=First Name]
  - text: No spaces or special characters. [ref=No spaces or special characters.]
  - text: Last Name [ref=Last Name]
  - textbox "Last Name" [ref=Last Name]
  - text: No spaces or special characters. [ref=No spaces or special characters.]
  - text: Date of Birth  [ref=Date of Birth ]
  - text: * [ref=*]
  - textbox "Date of Bir
2025-07-02 20:15:48,548 Failed to run server: unhandled errors in a TaskGroup (1 sub-exception)
2025-07-02 20:16:06,510 Processing request of type CallToolRequest
2025-07-02 20:16:06,510 url_match: <re.Match object; span=(14, 46), match='https://form2-4y5z.onrender.com/'>
2025-07-02 20:16:08,219 html: - WebArea: UserForm [ref=UserForm]
  - heading: User Registration [ref=User Registration]
  - text: First Name [ref=First Name]
  - textbox "First Name" [ref=First Name]
  - text: No spaces or special characters. [ref=No spaces or special characters.]
  - text: Last Name [ref=Last Name]
  - textbox "Last Name" [ref=Last Name]
  - text: No spaces or special characters. [ref=No spaces or special characters.]
  - text: Date of Birth  [ref=Date of Birth ]
  - text: * [ref=*]
  - textbox "Date of Bir
2025-07-02 20:19:54,561 Processing request of type CallToolRequest
2025-07-02 20:19:54,561 url_match: <re.Match object; span=(14, 46), match='https://form2-4y5z.onrender.com/'>
2025-07-02 20:19:56,020 html: - WebArea: UserForm [ref=UserForm]
  - heading: User Registration [ref=User Registration]
  - text: First Name [ref=First Name]
  - textbox "First Name" [ref=First Name]
  - text: No spaces or special characters. [ref=No spaces or special characters.]
  - text: Last Name [ref=Last Name]
  - textbox "Last Name" [ref=Last Name]
  - text: No spaces or special characters. [ref=No spaces or special characters.]
  - text: Date of Birth  [ref=Date of Birth ]
  - text: * [ref=*]
  - textbox "Date of Bir
