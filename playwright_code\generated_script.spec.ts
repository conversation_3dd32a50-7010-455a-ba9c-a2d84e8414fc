import { test, expect } from '@playwright/test';

test('fill and submit form', async ({ page }) => {
  await page.goto('https://form2-4y5z.onrender.com/');
  await page.getByRole('textbox', { name: 'First Name' }).fill('<PERSON>');
  await page.getByRole('textbox', { name: 'Last Name' }).fill('Doe');
  await page.getByRole('textbox', { name: 'Date of Birth *' }).fill('05-Mar-2000');
  await page.getByRole('textbox', { name: 'Phone Number *' }).fill('1234567890');
  await page.getByRole('textbox', { name: 'Email *' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Website' }).fill('www.johndoe.com');
  await page.getByRole('combobox', { name: 'Gender *' }).selectOption('Male');
  await page.getByRole('button', { name: 'Submit form' }).click();
});
