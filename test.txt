from mcp.server.fastmcp import Context
import os

# Get All html
async def get_html_content(ctx: Context, url: str) -> str:
    """
    Fetch the HTML content of a web page using Playwright.
    Args:
        ctx: MCP context (provides Playwright context)
        url: The URL of the page to fetch
    Returns:
        The HTML content as a string
    """
    try:
        browser = ctx.request_context.lifespan_context.browser
        page = await browser.new_page()
        await page.goto(url)
        content = await page.content()
        await page.close()
        return content
    except Exception as e:
        return f"Error fetching HTML: {str(e)}"
    

# Get html snapshot
async def get_html_snapshot(ctx: Context, url: str) -> str:
    """
    Get the HTML content of a web page using Playwright inside the MCP framework.

    Args:
        ctx: The MCP context which includes the browser instance.
        url: The URL of the web page to capture.

    Returns:
        A string containing the full HTML content of the rendered page.
    """
    try:
        # Get Playwright browser instance from MCP context
        browser = ctx.request_context.lifespan_context.browser
        
        # Create a new page
        page = await browser.new_page()

        # Go to the specified URL
        await page.goto(url, timeout=30000)  # 30 second timeout

        # Optionally wait for load completion
        await page.wait_for_load_state("networkidle")

        # Get the rendered HTML content
        html_content = await page.accessibility.snapshot()

        # Close the page
        await page.close()

        return html_content

    except Exception as e:
        return f"Error capturing snapshot of '{url}': {str(e)}"
    
# fill the fields and submit the form with a user comment
# Autofill and submit form
# async def autofill_form(ctx: Context, url: str, steps: list[dict]) -> str:
#     """
#     Autofill a form on the given URL using a sequence of user-defined steps.
    
#     Args:
#         ctx: MCP context containing the Playwright browser instance.
#         url: The URL of the page containing the form.
#         steps: A list of dictionaries representing form actions:
#                Each step should have 'action' ("fill" or "click"), 'selector', and optionally 'value'.

#     Example steps:
#         [
#             { "action": "fill", "selector": "#firstName", "value": "Saiju" },
#             { "action": "fill", "selector": "#lastName", "value": "Sunny" },
#             { "action": "fill", "selector": "#dob", "value": "05-Mar-2000" },
#             { "action": "fill", "selector": "#phone", "value": "9876543210" },
#             { "action": "fill", "selector": "#email", "value": "<EMAIL>" },
#             { "action": "select", "selector": "#gender", "value": "Others" },
#             { "action": "fill", "selector": "#otherGender", "value": "Non-binary" },
#             { "action": "click", "selector": "button[type='submit']" }
#         ]

#     Returns:
#         Final HTML after form interaction.
#     """
#     try:
#         browser = ctx.request_context.lifespan_context.browser
#         page = await browser.new_page()
#         await page.goto(url)
#         await page.wait_for_load_state("networkidle")

#         for step in steps:
#             action = step.get("action")
#             selector = step.get("selector")
#             value = step.get("value", "")

#             if action == "fill":
#                 await page.fill(selector, value)
#             elif action == "click":
#                 await page.click(selector)
#             else:
#                 return f"Unsupported action: {action}"

#         await page.wait_for_timeout(2000)  # Wait after submit (e.g., navigation)

#         content = await page.content()
#         await page.close()
#         return content

#     except Exception as e:
#         return f"Error autofilling form: {str(e)}"
    
######################
async def browser_snapshot(ctx: Context, params: dict) -> str:
    browser = ctx.request_context.lifespan_context.browser
    page = await browser.new_page()
    await page.goto("about:blank")  # Load a blank page
    snapshot = await page.accessibility.snapshot()
    await page.close()
    return snapshot

async def browser_click(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    selector = params["ref"]
    await page.click(selector)
    return f"Clicked on {params['element']}"


async def browser_hover(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    selector = params["ref"]
    await page.hover(selector)
    return f"Hovered over {params['element']}"


async def browser_drag(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    start = page.locator(params["startRef"])
    end = page.locator(params["endRef"])
    await start.drag_to(end)
    return f"Dragged from {params['startElement']} to {params['endElement']}"


async def browser_type(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    selector = params["ref"]
    text = params["text"]
    slowly = params.get("slowly", False)
    submit = params.get("submit", False)

    if slowly:
        for ch in text:
            await page.locator(selector).type(ch, delay=100)
    else:
        await page.fill(selector, text)

    if submit:
        await page.press(selector, "Enter")

    return f"Typed '{text}' into {params['element']}"


async def browser_upload(ctx: Context, params: dict) -> str:
    page = await ctx.ensure_page()
    selector = params["ref"]
    file_path = params["file"]

    if not os.path.exists(file_path):
        return f"File not found: {file_path}"

    input_handle = await page.query_selector(selector)
    await input_handle.set_input_files(file_path)

    return f"Uploaded file to {params['element']}"


async def browser_select_option(ctx: Context, params: dict) -> str:
    """
    Selects one or more options in a dropdown <select> field.

    Args:
        ctx: MCP context with Playwright browser.
        params: Dictionary with:
            - element: (str) Human-readable label
            - ref: (str) Selector string
            - values: (list[str]) Option values to select

    Example:
        {
          "element": "Gender Dropdown",
          "ref": "#gender",
          "values": ["Others"]
        }

    Returns:
        Success message or error.
    """
    try:
        page = await ctx.ensure_page()
        selector = params["ref"]
        values = params["values"]

        print(f"// Select {values} in {selector}")
        await page.select_option(selector, values)

        return f"Selected {values} in {params['element']}"

    except Exception as e:
        return f"Error selecting option: {str(e)}"
    

async def autofill_form(ctx: Context, url: str, steps: list[dict]) -> str:
    """
    Autofill a form on the given URL using a sequence of user-defined steps.

    Args:
        ctx: MCP context containing the Playwright browser instance.
        url: The URL of the page containing the form.
        steps: A list of dictionaries representing form actions:
               Each step should have 'action', 'selector', and optionally 'value'.

    Supported actions:
        - fill: Fill a field
        - click: Click a button or element
        - upload: Upload a file to an <input type="file">

    Example:
        [
            { "action": "fill", "selector": "#firstName", "value": "Saiju" },
            { "action": "fill", "selector": "#lastName", "value": "Sunny" },
            { "action": "fill", "selector": "#dob", "value": "05-Mar-2000" },
            { "action": "fill", "selector": "#phone", "value": "9876543210" },
            { "action": "fill", "selector": "#email", "value": "<EMAIL>" },
            { "action": "select", "selector": "#gender", "value": "Others" },
            { "action": "fill", "selector": "#otherGender", "value": "Non-binary" },
            { "action": "click", "selector": "button[type='submit']" }
        ]

    Returns:
        Final HTML after form interaction or error message.
    """
    try:
        browser = ctx.request_context.lifespan_context.browser
        page = await browser.new_page()
        await page.goto(url)
        await page.wait_for_load_state("networkidle")

        for step in steps:
            action = step.get("action")
            selector = step.get("selector")
            value = step.get("value", "")
    
            if action == "fill":
                print(f"// Fill '{value}' into {selector}")
                await page.fill(selector, value)
            elif action == "select":
                print(f"// Select {value} in {selector}")
                await page.select_option(selector, value)
            elif action == "click":
                print(f"// Click {selector}")
                await page.click(selector)

            elif action == "upload":
                print(f"// Upload '{value}' into {selector}")
                if not os.path.exists(value):
                    return f"File not found: {value}"
                input_handle = await page.query_selector(selector)
                if not input_handle:
                    return f"Input element not found: {selector}"
                await input_handle.set_input_files(value)

            else:
                return f"Unsupported action: {action}"

        await page.wait_for_timeout(2000)  # wait to allow form submission/navigation
        content = await page.content()
        await page.close()
        return content

    except Exception as e:
        return f"Error autofilling form: {str(e)}"
    
async def browser_check_checkbox(ctx: Context, params: dict) -> str:
    """
    Checks a checkbox input element.
    params: { "element": "label", "ref": "selector" }
    """
    try:
        page = await ctx.ensure_page()
        selector = params["ref"]
        await page.check(selector)
        return f"Checked checkbox {params['element']}"
    except Exception as e:
        return f"Error checking checkbox: {str(e)}"

async def browser_uncheck_checkbox(ctx: Context, params: dict) -> str:
    """
    Unchecks a checkbox input element.
    params: { "element": "label", "ref": "selector" }
    """
    try:
        page = await ctx.ensure_page()
        selector = params["ref"]
        await page.uncheck(selector)
        return f"Unchecked checkbox {params['element']}"
    except Exception as e:
        return f"Error unchecking checkbox: {str(e)}"

async def browser_select_radio_button(ctx: Context, params: dict) -> str:
    """
    Selects a radio button input element.
    params: { "element": "label", "ref": "selector" }
    """
    try:
        page = await ctx.ensure_page()
        selector = params["ref"]
        await page.check(selector)
        return f"Selected radio button {params['element']}"
    except Exception as e:
        return f"Error selecting radio button: {str(e)}"

async def browser_scroll_to_element(ctx: Context, params: dict) -> str:
    """
    Scrolls to a specific element on the page.
    params: { "element": "label", "ref": "selector" }
    """
    try:
        page = await ctx.ensure_page()
        selector = params["ref"]
        await page.locator(selector).scroll_into_view_if_needed()
        return f"Scrolled to {params['element']}"
    except Exception as e:
        return f"Error scrolling to element: {str(e)}"

async def browser_scroll_to_top(ctx: Context, params: dict) -> str:
    """
    Scrolls to the top of the page.
    """
    try:
        page = await ctx.ensure_page()
        await page.evaluate("window.scrollTo(0, 0);")
        return "Scrolled to top of the page"
    except Exception as e:
        return f"Error scrolling to top: {str(e)}"

async def browser_scroll_to_bottom(ctx: Context, params: dict) -> str:
    """
    Scrolls to the bottom of the page.
    """
    try:
        page = await ctx.ensure_page()
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight);")
        return "Scrolled to bottom of the page"
    except Exception as e:
        return f"Error scrolling to bottom: {str(e)}"

async def browser_scroll_to_left(ctx: Context, params: dict) -> str:
    """
    Scrolls to the leftmost position of the page.
    """
    try:
        page = await ctx.ensure_page()
        await page.evaluate("window.scrollTo(0, window.scrollY);")
        return "Scrolled to left of the page"
    except Exception as e:
        return f"Error scrolling to left: {str(e)}"

async def browser_scroll_to_right(ctx: Context, params: dict) -> str:
    """
    Scrolls to the rightmost position of the page.
    """
    try:
        page = await ctx.ensure_page()
        await page.evaluate("window.scrollTo(document.body.scrollWidth, window.scrollY);")
        return "Scrolled to right of the page"
    except Exception as e:
        return f"Error scrolling to right: {str(e)}"

